import * as React from 'react';
import * as <PERSON><PERSON><PERSON><PERSON> from 'react-dom';
import * as ReactJsxDevRuntime from 'react/jsx-dev-runtime';
import * as ReactJsxRuntime from 'react/jsx-runtime';
import * as ReactCompilerRuntime from 'react/compiler-runtime';
declare let ReactServerDOMTurbopackServer: any, ReactServerDOMWebpackServer: any;
declare let ReactServerDOMTurbopackStatic: any, ReactServerDOMWebpackStatic: any;
export { React, ReactJsxDevRuntime, ReactJsxRuntime, ReactCompilerRuntime, ReactDOM, ReactServerDOMTurbopackServer, ReactServerDOMTurbopackStatic, ReactServerDOMWebpackServer, ReactServerDOMWebpackStatic, };
