{"version": 3, "sources": ["../../../src/server/async-storage/with-store.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n/**\n * Implementations provide a wrapping function that will provide the storage to\n * async calls derived from the provided callback function.\n *\n * @param storage underlying storage object\n * @param context context used to create the storage object\n * @param callback function to call within the scope of the storage\n * @returns the result of the callback\n */\nexport type WithStore<Store extends {}, Context extends {}> = <Result>(\n  storage: AsyncLocalStorage<Store>,\n  context: Context,\n  callback: (store: Store) => Result\n) => Result\n"], "names": [], "mappings": "AAEA;;;;;;;;CAQC,GACD,WAIW", "ignoreList": [0]}