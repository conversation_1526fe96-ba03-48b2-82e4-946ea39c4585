{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/etheral-shadow.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useId, useEffect, CSSProperties } from 'react';\nimport { animate, useMotionValue, AnimationPlaybackControls } from 'framer-motion';\n\n// Type definitions\ninterface ResponsiveImage {\n    src: string;\n    alt?: string;\n    srcSet?: string;\n}\n\ninterface AnimationConfig {\n    preview?: boolean;\n    scale: number;\n    speed: number;\n}\n\ninterface NoiseConfig {\n    opacity: number;\n    scale: number;\n}\n\ninterface ShadowOverlayProps {\n    type?: 'preset' | 'custom';\n    presetIndex?: number;\n    customImage?: ResponsiveImage;\n    sizing?: 'fill' | 'stretch';\n    color?: string;\n    animation?: AnimationConfig;\n    noise?: NoiseConfig;\n    style?: CSSProperties;\n    className?: string;\n}\n\nfunction mapRange(\n    value: number,\n    fromLow: number,\n    fromHigh: number,\n    toLow: number,\n    toHigh: number\n): number {\n    if (fromLow === fromHigh) {\n        return toLow;\n    }\n    const percentage = (value - fromLow) / (fromHigh - fromLow);\n    return toLow + percentage * (toHigh - toLow);\n}\n\nconst useInstanceId = (): string => {\n    const id = useId();\n    const cleanId = id.replace(/:/g, \"\");\n    const instanceId = `shadowoverlay-${cleanId}`;\n    return instanceId;\n};\n\nexport function Component({\n    sizing = 'fill',\n    color = 'rgba(128, 128, 128, 1)',\n    animation,\n    noise,\n    style,\n    className\n}: ShadowOverlayProps) {\n    const id = useInstanceId();\n    const animationEnabled = animation && animation.scale > 0;\n    const feColorMatrixRef = useRef<SVGFEColorMatrixElement>(null);\n    const hueRotateMotionValue = useMotionValue(180);\n    const hueRotateAnimation = useRef<AnimationPlaybackControls | null>(null);\n\n    const displacementScale = animation ? mapRange(animation.scale, 1, 100, 20, 100) : 0;\n    const animationDuration = animation ? mapRange(animation.speed, 1, 100, 1000, 50) : 1;\n\n    useEffect(() => {\n        if (feColorMatrixRef.current && animationEnabled) {\n            if (hueRotateAnimation.current) {\n                hueRotateAnimation.current.stop();\n            }\n            hueRotateMotionValue.set(0);\n            hueRotateAnimation.current = animate(hueRotateMotionValue, 360, {\n                duration: animationDuration / 25,\n                repeat: Infinity,\n                repeatType: \"loop\",\n                repeatDelay: 0,\n                ease: \"linear\",\n                delay: 0,\n                onUpdate: (value: number) => {\n                    if (feColorMatrixRef.current) {\n                        feColorMatrixRef.current.setAttribute(\"values\", String(value));\n                    }\n                }\n            });\n\n            return () => {\n                if (hueRotateAnimation.current) {\n                    hueRotateAnimation.current.stop();\n                }\n            };\n        }\n    }, [animationEnabled, animationDuration, hueRotateMotionValue]);\n\n    return (\n        <div\n            className={className}\n            style={{\n                overflow: \"hidden\",\n                position: \"relative\",\n                width: \"100%\",\n                height: \"100%\",\n                ...style\n            }}\n        >\n            <div\n                style={{\n                    position: \"absolute\",\n                    inset: -displacementScale,\n                    filter: animationEnabled ? `url(#${id}) blur(4px)` : \"none\"\n                }}\n            >\n                {animationEnabled && (\n                    <svg style={{ position: \"absolute\" }}>\n                        <defs>\n                            <filter id={id}>\n                                <feTurbulence\n                                    result=\"undulation\"\n                                    numOctaves=\"2\"\n                                    baseFrequency={`${mapRange(animation.scale, 0, 100, 0.001, 0.0005)},${mapRange(animation.scale, 0, 100, 0.004, 0.002)}`}\n                                    seed=\"0\"\n                                    type=\"turbulence\"\n                                />\n                                <feColorMatrix\n                                    ref={feColorMatrixRef}\n                                    in=\"undulation\"\n                                    type=\"hueRotate\"\n                                    values=\"180\"\n                                />\n                                <feColorMatrix\n                                    in=\"dist\"\n                                    result=\"circulation\"\n                                    type=\"matrix\"\n                                    values=\"4 0 0 0 1  4 0 0 0 1  4 0 0 0 1  1 0 0 0 0\"\n                                />\n                                <feDisplacementMap\n                                    in=\"SourceGraphic\"\n                                    in2=\"circulation\"\n                                    scale={displacementScale}\n                                    result=\"dist\"\n                                />\n                                <feDisplacementMap\n                                    in=\"dist\"\n                                    in2=\"undulation\"\n                                    scale={displacementScale}\n                                    result=\"output\"\n                                />\n                            </filter>\n                        </defs>\n                    </svg>\n                )}\n                <div\n                    style={{\n                        backgroundColor: color,\n                        maskImage: `url('https://framerusercontent.com/images/ceBGguIpUU8luwByxuQz79t7To.png')`,\n                        maskSize: sizing === \"stretch\" ? \"100% 100%\" : \"cover\",\n                        maskRepeat: \"no-repeat\",\n                        maskPosition: \"center\",\n                        width: \"100%\",\n                        height: \"100%\"\n                    }}\n                />\n            </div>\n\n            <div\n                style={{\n                    position: \"absolute\",\n                    top: \"50%\",\n                    left: \"50%\",\n                    transform: \"translate(-50%, -50%)\",\n                    textAlign: \"center\",\n                    zIndex: 10\n                }}\n            >\n                <h1 className=\"md:text-7xl text-6xl lg:text-8xl font-bold text-center text-foreground relative z-20\">\n                    Etheral Shadows\n                </h1>\n            </div>\n\n            {noise && noise.opacity > 0 && (\n                <div\n                    style={{\n                        position: \"absolute\",\n                        inset: 0,\n                        backgroundImage: `url(\"https://framerusercontent.com/images/g0QcWrxr87K0ufOxIUFBakwYA8.png\")`,\n                        backgroundSize: noise.scale * 200,\n                        backgroundRepeat: \"repeat\",\n                        opacity: noise.opacity / 2\n                    }}\n                />\n            )}\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAmCA,SAAS,SACL,KAAa,EACb,OAAe,EACf,QAAgB,EAChB,KAAa,EACb,MAAc;IAEd,IAAI,YAAY,UAAU;QACtB,OAAO;IACX;IACA,MAAM,aAAa,CAAC,QAAQ,OAAO,IAAI,CAAC,WAAW,OAAO;IAC1D,OAAO,QAAQ,aAAa,CAAC,SAAS,KAAK;AAC/C;AAEA,MAAM,gBAAgB;IAClB,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACf,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM;IACjC,MAAM,aAAa,CAAC,cAAc,EAAE,SAAS;IAC7C,OAAO;AACX;AAEO,SAAS,UAAU,EACtB,SAAS,MAAM,EACf,QAAQ,wBAAwB,EAChC,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACQ;IACjB,MAAM,KAAK;IACX,MAAM,mBAAmB,aAAa,UAAU,KAAK,GAAG;IACxD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACzD,MAAM,uBAAuB,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC5C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoC;IAEpE,MAAM,oBAAoB,YAAY,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK,IAAI,OAAO;IACnF,MAAM,oBAAoB,YAAY,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK,MAAM,MAAM;IAEpF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,iBAAiB,OAAO,IAAI,kBAAkB;YAC9C,IAAI,mBAAmB,OAAO,EAAE;gBAC5B,mBAAmB,OAAO,CAAC,IAAI;YACnC;YACA,qBAAqB,GAAG,CAAC;YACzB,mBAAmB,OAAO,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,KAAK;gBAC5D,UAAU,oBAAoB;gBAC9B,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,UAAU,CAAC;oBACP,IAAI,iBAAiB,OAAO,EAAE;wBAC1B,iBAAiB,OAAO,CAAC,YAAY,CAAC,UAAU,OAAO;oBAC3D;gBACJ;YACJ;YAEA,OAAO;gBACH,IAAI,mBAAmB,OAAO,EAAE;oBAC5B,mBAAmB,OAAO,CAAC,IAAI;gBACnC;YACJ;QACJ;IACJ,GAAG;QAAC;QAAkB;QAAmB;KAAqB;IAE9D,qBACI,8OAAC;QACG,WAAW;QACX,OAAO;YACH,UAAU;YACV,UAAU;YACV,OAAO;YACP,QAAQ;YACR,GAAG,KAAK;QACZ;;0BAEA,8OAAC;gBACG,OAAO;oBACH,UAAU;oBACV,OAAO,CAAC;oBACR,QAAQ,mBAAmB,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG;gBACzD;;oBAEC,kCACG,8OAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAW;kCAC/B,cAAA,8OAAC;sCACG,cAAA,8OAAC;gCAAO,IAAI;;kDACR,8OAAC;wCACG,QAAO;wCACP,YAAW;wCACX,eAAe,GAAG,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK,OAAO,QAAQ,CAAC,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK,OAAO,QAAQ;wCACvH,MAAK;wCACL,MAAK;;;;;;kDAET,8OAAC;wCACG,KAAK;wCACL,IAAG;wCACH,MAAK;wCACL,QAAO;;;;;;kDAEX,8OAAC;wCACG,IAAG;wCACH,QAAO;wCACP,MAAK;wCACL,QAAO;;;;;;kDAEX,8OAAC;wCACG,IAAG;wCACH,KAAI;wCACJ,OAAO;wCACP,QAAO;;;;;;kDAEX,8OAAC;wCACG,IAAG;wCACH,KAAI;wCACJ,OAAO;wCACP,QAAO;;;;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC;wBACG,OAAO;4BACH,iBAAiB;4BACjB,WAAW,CAAC,0EAA0E,CAAC;4BACvF,UAAU,WAAW,YAAY,cAAc;4BAC/C,YAAY;4BACZ,cAAc;4BACd,OAAO;4BACP,QAAQ;wBACZ;;;;;;;;;;;;0BAIR,8OAAC;gBACG,OAAO;oBACH,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,WAAW;oBACX,WAAW;oBACX,QAAQ;gBACZ;0BAEA,cAAA,8OAAC;oBAAG,WAAU;8BAAuF;;;;;;;;;;;YAKxG,SAAS,MAAM,OAAO,GAAG,mBACtB,8OAAC;gBACG,OAAO;oBACH,UAAU;oBACV,OAAO;oBACP,iBAAiB,CAAC,0EAA0E,CAAC;oBAC7F,gBAAgB,MAAM,KAAK,GAAG;oBAC9B,kBAAkB;oBAClB,SAAS,MAAM,OAAO,GAAG;gBAC7B;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ThemeToggleProps {\n  className?: string\n}\n\nexport function ThemeToggle({ className }: ThemeToggleProps) {\n  const [mounted, setMounted] = useState(false)\n  const { resolvedTheme, setTheme } = useTheme()\n  \n  // 避免水合不匹配\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return null\n  }\n\n  const isDark = resolvedTheme === \"dark\"\n\n  return (\n    <div\n      className={cn(\n        \"flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300\",\n        isDark \n          ? \"bg-zinc-950 border border-zinc-800\" \n          : \"bg-white border border-zinc-200\",\n        className\n      )}\n      onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\n      role=\"button\"\n      tabIndex={0}\n    >\n      <div className=\"flex justify-between items-center w-full\">\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark \n              ? \"transform translate-x-0 bg-zinc-800\" \n              : \"transform translate-x-8 bg-gray-200\"\n          )}\n        >\n          {isDark ? (\n            <Moon \n              className=\"w-4 h-4 text-white\" \n              strokeWidth={1.5}\n            />\n          ) : (\n            <Sun \n              className=\"w-4 h-4 text-gray-700\" \n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark \n              ? \"bg-transparent\" \n              : \"transform -translate-x-8\"\n          )}\n        >\n          {isDark ? (\n            <Sun \n              className=\"w-4 h-4 text-gray-500\" \n              strokeWidth={1.5}\n            />\n          ) : (\n            <Moon \n              className=\"w-4 h-4 text-black\" \n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAWO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE3C,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,SAAS,kBAAkB;IAEjC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6EACA,SACI,uCACA,mCACJ;QAEF,SAAS,IAAM,SAAS,SAAS,UAAU;QAC3C,MAAK;QACL,UAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,wCACA;8BAGL,uBACC,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;6CAGf,8OAAC,gMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;;;;;;;8BAInB,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,mBACA;8BAGL,uBACC,8OAAC,gMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;6CAGf,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}]}