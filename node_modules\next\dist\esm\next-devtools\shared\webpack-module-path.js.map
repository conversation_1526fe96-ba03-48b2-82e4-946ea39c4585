{"version": 3, "sources": ["../../../src/next-devtools/shared/webpack-module-path.ts"], "sourcesContent": ["const replacementRegExes = [\n  /^webpack-internal:\\/\\/\\/(\\([\\w-]+\\)\\/)?/,\n  /^(webpack:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)(\\([\\w-]+\\)\\/)?/,\n]\n\nexport function isWebpackInternalResource(file: string) {\n  for (const regex of replacementRegExes) {\n    if (regex.test(file)) return true\n\n    file = file.replace(regex, '')\n  }\n\n  return false\n}\n\n/**\n * Format the webpack internal id to original file path\n *\n * webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n * webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n * webpack://./src/hello.tsx => ./src/hello.tsx\n * webpack:///./src/hello.tsx => ./src/hello.tsx\n */\nexport function formatFrameSourceFile(file: string) {\n  for (const regex of replacementRegExes) {\n    file = file.replace(regex, '')\n  }\n\n  return file\n}\n"], "names": ["replacementRegExes", "isWebpackInternalResource", "file", "regex", "test", "replace", "formatFrameSourceFile"], "mappings": "AAAA,MAAMA,qBAAqB;IACzB;IACA;CACD;AAED,OAAO,SAASC,0BAA0BC,IAAY;IACpD,KAAK,MAAMC,SAASH,mBAAoB;QACtC,IAAIG,MAAMC,IAAI,CAACF,OAAO,OAAO;QAE7BA,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASG,sBAAsBJ,IAAY;IAChD,KAAK,MAAMC,SAASH,mBAAoB;QACtCE,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAOD;AACT", "ignoreList": [0]}