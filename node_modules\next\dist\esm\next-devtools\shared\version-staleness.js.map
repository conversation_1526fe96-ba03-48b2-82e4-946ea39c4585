{"version": 3, "sources": ["../../../src/next-devtools/shared/version-staleness.ts"], "sourcesContent": ["import type { VersionInfo } from '../../server/dev/parse-version-info'\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js ${installed}`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} (stale)`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} (outdated)`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} (stale)`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      text = `${versionLabel} (unknown)`\n      title = 'No Next.js version data was found.'\n      indicatorClass = 'unknown'\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n"], "names": ["getStaleness", "installed", "staleness", "expected", "text", "title", "indicatorClass", "versionLabel"], "mappings": "AAEA,OAAO,SAASA,aAAa,KAA+C;IAA/C,IAAA,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAe,GAA/C;IAC3B,IAAIC,OAAO;IACX,IAAIC,QAAQ;IACZ,IAAIC,iBAAiB;IACrB,MAAMC,eAAe,AAAC,aAAUN;IAChC,OAAQC;QACN,KAAK;QACL,KAAK;YACHE,OAAOG;YACPF,QAAQ,AAAC,2CAAwCJ,YAAU;YAC3DK,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHF,OAAO,AAAC,KAAEG,eAAa;YACvBF,QAAQ,AAAC,+BAA4BF,WAAS;YAC9CG,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBF,OAAO,AAAC,KAAEG,eAAa;gBACvBF,QAAQ,AAAC,6CAA0CF,WAAS;gBAC5DG,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBF,OAAO,AAAC,KAAEG,eAAa;gBACvBF,QAAQ,AAAC,sCAAmCF,WAAS;gBACrDG,iBAAiB;gBACjB;YACF;QACA,KAAK;YACHF,OAAO,AAAC,KAAEG,eAAa;YACvBF,QAAQ;YACRC,iBAAiB;YACjB;QACF;YACE;IACJ;IACA,OAAO;QAAEF;QAAME;QAAgBD;IAAM;AACvC", "ignoreList": [0]}