{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/etheral-shadow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Component = registerClientReference(\n    function() { throw new Error(\"Attempted to call Component() from the server but Component is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/etheral-shadow.tsx <module evaluation>\",\n    \"Component\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/etheral-shadow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Component = registerClientReference(\n    function() { throw new Error(\"Attempted to call Component() from the server but Component is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/etheral-shadow.tsx\",\n    \"Component\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/demo.tsx"], "sourcesContent": ["import { Component } from \"@/components/ui/etheral-shadow\";\n\nconst DemoOne = () => {\n  return (\n    <div className=\"flex w-full h-screen justify-center items-center\">\n      <Component\n        color=\"rgba(128, 128, 128, 1)\"\n        animation={{ scale: 100, speed: 90 }}\n        noise={{ opacity: 1, scale: 1.2 }}\n        sizing=\"fill\"\n      />\n    </div>\n  );\n};\n\nexport { DemoOne };\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,6IAAA,CAAA,YAAS;YACR,OAAM;YACN,WAAW;gBAAE,OAAO;gBAAK,OAAO;YAAG;YACnC,OAAO;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAChC,QAAO;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/components/ui/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gDACA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/co/src/app/page.tsx"], "sourcesContent": ["import { DemoOne } from \"@/components/ui/demo\";\nimport { ThemeToggle } from \"@/components/ui/theme-toggle\";\n\nexport default function Home() {\n  return (\n    <div className=\"w-full h-screen relative\">\n      {/* 主题切换按钮 - 右上角 */}\n      <div className=\"absolute top-4 right-4 z-50\">\n        <ThemeToggle />\n      </div>\n\n      {/* 主要内容 */}\n      <DemoOne />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2IAAA,CAAA,cAAW;;;;;;;;;;0BAId,8OAAC,gIAAA,CAAA,UAAO;;;;;;;;;;;AAGd", "debugId": null}}]}